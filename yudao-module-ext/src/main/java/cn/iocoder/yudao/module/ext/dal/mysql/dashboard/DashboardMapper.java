package cn.iocoder.yudao.module.ext.dal.mysql.dashboard;

import java.util.*;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ext.dal.dataobject.dashboard.DashboardDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ext.controller.admin.dashboard.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 工作台设置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DashboardMapper extends BaseMapperX<DashboardDO> {

    /**
     * 获取工作台设置列表
     *
     * @param userId      用户id
     * @param dashGroup   工作组
     * @return 工作台设置列表
     */
    @Select("<script>" +
            "SELECT a.id, a.dashlet_id, a.cus_col as col, a.`cus_row` as `row`, a.cus_sort as sort, a.dash_group,a.user_id " +
            ",b.`name` AS dash_name, b.`status`, b.component_name, b.params AS dash_params, b.body AS dash_body, b.url " +
            "FROM ext_dashboard a " +
            "INNER JOIN ext_dashlet b ON a.dashlet_id=b.id " +
            "WHERE a.deleted=0 AND a.dash_group=#{dashGroup} " +
            "<if test='userId != null'> AND a.user_id = #{userId} </if>" +
            "<if test='userId == null'> AND a.user_id IS NULL </if> " +
            "ORDER BY sort " +
            "</script>")
    List<DashboardRespVO> getListByUserIdAndDashGroup(@Param("userId") Long userId, @Param("dashGroup") String dashGroup);

}

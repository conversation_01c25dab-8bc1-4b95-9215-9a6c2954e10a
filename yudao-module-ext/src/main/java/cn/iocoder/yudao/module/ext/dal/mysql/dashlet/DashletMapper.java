package cn.iocoder.yudao.module.ext.dal.mysql.dashlet;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ext.dal.dataobject.dashlet.DashletDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ext.controller.admin.dashlet.vo.*;

/**
 * 工作台模块管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DashletMapper extends BaseMapperX<DashletDO> {

    default PageResult<DashletDO> selectPage(DashletPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DashletDO>()
                .likeIfPresent(DashletDO::getName, reqVO.getName())
                .orderByDesc(DashletDO::getId));
    }

}
package cn.iocoder.yudao.module.ext.controller.admin.dashboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 工作台设置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DashboardRespVO {

    @Schema(description = "模块ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25753")
    private Long id;

    @Schema(description = "模块名称")
    private String dashName;

    @Schema(description = "工作台模块name")
    private String componentName;

    @Schema(description = "模块id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26183")
    private Long dashletId;

    @Schema(description = "列")
    private Integer col;

    @Schema(description = "行")
    private Integer row;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sort;

    @Schema(description = "用户id", example = "28984")
    private Long userId;

    @Schema(description = "工作台组")
    private String dashGroup;

    @Schema(description = "是否默认")
    private Boolean isDefault;

    @Schema(description = "用户个人参数")
    private String params;

    @Schema(description = "路径")
    private String url;

    @Schema(description = "模块params")
    private String dashParams;

    @Schema(description = "模块body")
    private String dashBody;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer status;

}

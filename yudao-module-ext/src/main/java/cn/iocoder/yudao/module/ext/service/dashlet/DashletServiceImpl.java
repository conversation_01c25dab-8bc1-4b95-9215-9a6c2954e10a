package cn.iocoder.yudao.module.ext.service.dashlet;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.MenuDO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import cn.iocoder.yudao.module.ext.controller.admin.dashlet.vo.*;
import cn.iocoder.yudao.module.ext.dal.dataobject.dashlet.DashletDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ext.dal.mysql.dashlet.DashletMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.*;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.ext.enums.ErrorCodeConstants.*;

/**
 * 工作台模块管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DashletServiceImpl implements DashletService {

    @Resource
    private DashletMapper dashletMapper;

    @Resource
    private PermissionApi permissionApi;

    @Override
    public Long createDashlet(DashletSaveReqVO createReqVO) {
        // 插入
        DashletDO dashlet = BeanUtils.toBean(createReqVO, DashletDO.class);
        dashletMapper.insert(dashlet);
        // 返回
        return dashlet.getId();
    }

    @Override
    public void updateDashlet(DashletSaveReqVO updateReqVO) {
        // 校验存在
        validateDashletExists(updateReqVO.getId());
        // 更新
        DashletDO updateObj = BeanUtils.toBean(updateReqVO, DashletDO.class);
        dashletMapper.updateById(updateObj);
    }

    @Override
    public void deleteDashlet(Long id) {
        // 校验存在
        validateDashletExists(id);
        // 删除
        dashletMapper.deleteById(id);
    }

    @Override
    public void deleteDashletListByIds(List<Long> ids) {
        // 校验存在
        validateDashletExists(ids);
        // 删除
        dashletMapper.deleteByIds(ids);
    }

    private void validateDashletExists(List<Long> ids) {
        List<DashletDO> list = dashletMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(DASHLET_NOT_EXISTS);
        }
    }

    private void validateDashletExists(Long id) {
        if (dashletMapper.selectById(id) == null) {
            throw exception(DASHLET_NOT_EXISTS);
        }
    }

    @Override
    public DashletDO getDashlet(Long id) {
        return dashletMapper.selectById(id);
    }

    @Override
    public PageResult<DashletDO> getDashletPage(DashletPageReqVO pageReqVO) {
        return dashletMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DashletDO> getMyPermissionDashletList(List<String> publicNames) {
        Long userId = getLoginUserId();
        if (userId == null) {
            return Collections.emptyList();
        }
        List<MenuDO> menuList = permissionApi.getUserDashboardMenuList(userId);
        Set<String> componentNames = convertSet(menuList, MenuDO::getComponentName);
        if (!CollUtil.isEmpty(publicNames)) {
            componentNames.addAll(publicNames);
        }

        if (CollUtil.isEmpty(componentNames)) {
            return Collections.emptyList();
        }

        return dashletMapper.selectList(
                new LambdaQueryWrapperX<DashletDO>()
                        .in(DashletDO::getComponentName, componentNames)
                        .orderByAsc(DashletDO::getSort)
        );
    }
}

package cn.iocoder.yudao.module.ext.dal.dataobject.dashlet;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 工作台模块管理 DO
 *
 * <AUTHOR>
 */
@TableName("ext_dashlet")
@KeySequence("ext_dashlet_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DashletDO extends BaseDO {

    /**
     * 模块ID
     */
    @TableId
    private Long id;
    /**
     * 模块名称
     */
    private String name;
    /**
     * 显示顺序
     */
    private Integer sort;
    /**
     * 默认列
     */
    private Integer defCol;
    /**
     * 默认默认行
     */
    private Integer defRow;
    /**
     * 模块icon
     */
    private String icon;
    /**
     * 模块url
     */
    private String url;
    /**
     * 参数
     */
    private String params;
    /**
     * body参数
     */
    private String body;
    /**
     * 模块描述
     */
    private String description;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 前端组件名称
     */
    private String componentName;


}
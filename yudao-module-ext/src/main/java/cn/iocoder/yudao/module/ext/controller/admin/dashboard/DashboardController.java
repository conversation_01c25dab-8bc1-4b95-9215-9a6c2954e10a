package cn.iocoder.yudao.module.ext.controller.admin.dashboard;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ext.controller.admin.dashboard.vo.*;
import cn.iocoder.yudao.module.ext.dal.dataobject.dashboard.DashboardDO;
import cn.iocoder.yudao.module.ext.service.dashboard.DashboardService;

@Tag(name = "管理后台 - 工作台设置")
@RestController
@RequestMapping("/admin/ext/dashboard")
@Validated
public class DashboardController {

    @Resource
    private DashboardService dashboardService;

    @GetMapping("/my-list")
    @Operation(summary = "获取我的工作台模块")
    @Parameter(name = "group", description = "分组", required = false, example = "1024")
    public CommonResult<?> getMyDashboardList(@RequestParam(required = false) String group) {
        return success(dashboardService.getMyList(group));
    }

    @PostMapping("/config-my-dashboards")
    @Operation(summary = "配置我的工作台模块")
    public CommonResult<Boolean> configMyDashboards(@Validated @RequestBody List<DashboardSaveReqVO> vos) {
        if (vos.isEmpty()) {
            return success(false);
        }
        dashboardService.configMyDashboards(vos);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除工作台设置")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteDashboard(@RequestParam("id") Long id) {
        dashboardService.deleteDashboard(id);
        return success(true);
    }

}

package cn.iocoder.yudao.module.ext.controller.admin.dashboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 工作台设置新增/修改 Request VO")
@Data
public class DashboardSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25753")
    private Long id;

    @Schema(description = "模块id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26183")
    @NotNull(message = "模块id不能为空")
    private Long dashletId;

    @Schema(description = "列")
    @NotNull(message = "列不能为空")
    private Integer col;

    @Schema(description = "行")
    @NotNull(message = "行不能为空")
    private Integer row;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    @Schema(description = "用户id", example = "28984")
    private Long userId;

    @Schema(description = "工作台组")
    private String dashGroup;

    @Schema(description = "是否默认")
    private Boolean isDefault;

}

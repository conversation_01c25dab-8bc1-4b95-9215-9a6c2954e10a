package cn.iocoder.yudao.module.ext.controller.admin.dashlet.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 工作台模块管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DashletRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("id")
    private Integer id;

    @Schema(description = "模块名称", example = "芋艿")
    @ExcelProperty("模块名称")
    private String name;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("显示顺序")
    private Integer sort;

    @Schema(description = "默认列")
    @ExcelProperty("默认列")
    private Integer defCol;

    @Schema(description = "默认行")
    @ExcelProperty("默认行")
    private Integer defRow;

    @Schema(description = "模块icon")
    @ExcelProperty("模块icon")
    private String icon;

    @Schema(description = "模块url", example = "https://www.iocoder.cn")
    @ExcelProperty("模块url")
    private String url;

    @Schema(description = "模块描述", example = "你说的对")
    @ExcelProperty("模块描述")
    private String description;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "前端组件名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("前端组件名称")
    private String componentName;

}

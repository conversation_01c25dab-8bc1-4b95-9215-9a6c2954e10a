package cn.iocoder.yudao.module.ext.controller.admin.dashlet.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 工作台模块管理分页 Request VO")
@Data
public class DashletPageReqVO extends PageParam {

    @Schema(description = "模块名称", example = "芋艿")
    private String name;

}
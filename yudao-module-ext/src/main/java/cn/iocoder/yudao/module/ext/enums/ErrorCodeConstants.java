package cn.iocoder.yudao.module.ext.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 扩展模块 错误码枚举类
 * <p>
 * 扩展模块，使用 1-099-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 标签分类 1_099_000_001 ==========
    ErrorCode TAG_CATEGORY_NOT_EXISTS = new ErrorCode(1_099_000_001, "标签分类不存在");
    ErrorCode TAG_CATEGORY_EXITS_CHILDREN = new ErrorCode(1_099_000_002, "存在存在子标签分类，无法删除");
    ErrorCode TAG_CATEGORY_PARENT_NOT_EXITS = new ErrorCode(1_099_000_003,"父级标签分类不存在");
    ErrorCode TAG_CATEGORY_PARENT_ERROR = new ErrorCode(1_099_000_004, "不能设置自己为父标签分类");
    ErrorCode TAG_CATEGORY_NAME_DUPLICATE = new ErrorCode(1_099_000_005, "已经存在该分类名称的标签分类");
    ErrorCode TAG_CATEGORY_PARENT_IS_CHILD = new ErrorCode(1_099_000_006, "不能设置自己的子TagCategory为父TagCategory");

    ErrorCode TAG_DEFINE_NOT_EXISTS = new ErrorCode(1_099_000_101, "标签定义不存在");
    ErrorCode DASHLET_NOT_EXISTS = new ErrorCode(1_099_000_102, "工作台模块管理不存在");
    ErrorCode DASHBOARD_NOT_EXISTS = new ErrorCode(1_099_000_103, "工作台设置不存在");
}

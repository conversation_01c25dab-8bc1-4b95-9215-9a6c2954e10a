package cn.iocoder.yudao.module.ext.dal.dataobject.dashboard;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 工作台设置 DO
 *
 * <AUTHOR>
 */
@TableName("ext_dashboard")
@KeySequence("ext_dashboard_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DashboardDO extends BaseDO {

    /**
     * 模块ID
     */
    @TableId
    private Long id;
    /**
     * 模块id
     */
    private Long dashletId;
    /**
     * 列
     */
    private Integer cusCol;
    /**
     * 行
     */
    private Integer cusRow;
    /**
     * 显示顺序
     */
    private Integer cusSort;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 工作台组
     */
    private String dashGroup;
    /**
     * 是否默认
     *
     */
    private Boolean isDefault;
    /**
     * 用户个人参数
     */
    private String params;
    /**
     * 菜单状态
     *
     */
    private Integer status;


}

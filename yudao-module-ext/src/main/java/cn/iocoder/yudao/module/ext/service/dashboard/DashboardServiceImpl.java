package cn.iocoder.yudao.module.ext.service.dashboard;

import cn.hutool.core.collection.CollUtil;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.yudao.module.ext.controller.admin.dashboard.vo.*;
import cn.iocoder.yudao.module.ext.dal.dataobject.dashboard.DashboardDO;

import cn.iocoder.yudao.module.ext.dal.mysql.dashboard.DashboardMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.*;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.ext.enums.ErrorCodeConstants.*;

/**
 * 工作台设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DashboardServiceImpl implements DashboardService {

    @Resource
    private DashboardMapper dashboardMapper;

    @Override
    public List<DashboardRespVO> getMyList(String group) {
        Long userId = getLoginUserId();
        if (userId == null) {
            return new ArrayList<>();
        }

        if (!StringUtils.hasLength(group)) {
            group = "默认";
        }

        List<DashboardRespVO> list = dashboardMapper.getListByUserIdAndDashGroup(userId, group);
        if (CollUtil.isEmpty(list)) {
            list = dashboardMapper.getListByUserIdAndDashGroup(null, group);
        }

        return list;
    }

    @Override
    public void configMyDashboards(@Valid List<DashboardSaveReqVO> list) {
        Long userId = getLoginUserId();
        DashboardSaveReqVO reqVO = list.get(0);
        String group = reqVO.getDashGroup();
        if (!StringUtils.hasLength(group)) {
            group = "默认";
        }
        List<DashboardRespVO> oldList = dashboardMapper.getListByUserIdAndDashGroup(userId, group);
        List<DashboardDO> dashboardList = new ArrayList<>(list.size());
        for (DashboardSaveReqVO dashboardSaveReqVO : list) {
            DashboardDO dashboardDO = null;
            if (dashboardSaveReqVO.getUserId() != null && dashboardSaveReqVO.getId() != null) {
                // 如果用户ID不为空，获取ID也不为空，认为用户已经保存过了，这时候需要更新
                dashboardDO = dashboardMapper.selectById(dashboardSaveReqVO.getId());
                if (dashboardDO != null) {
                    // 更新
                    dashboardDO.setCusCol(dashboardSaveReqVO.getCol());
                    dashboardDO.setCusRow(dashboardSaveReqVO.getRow());
                    dashboardDO.setCusSort(dashboardSaveReqVO.getSort());
                    dashboardDO.setDashGroup(group);
                }
            }

            if (dashboardDO == null) {
                dashboardDO = DashboardDO.builder()
                        .dashletId(dashboardSaveReqVO.getDashletId())
                        .cusCol(dashboardSaveReqVO.getCol())
                        .cusRow(dashboardSaveReqVO.getRow())
                        .cusSort(dashboardSaveReqVO.getSort())
                        .userId(userId)
                        .dashGroup(group)
                        .isDefault(true) // 需要分组的时候再加
                        .status(0)
                        .build();
            }
            dashboardList.add(dashboardDO);
        }
        dashboardMapper.insertOrUpdate(dashboardList, dashboardList.size());

        // 找出存量数据有但是新数据里没有的数据，然后删除
        Map<Long, DashboardDO> updateMap = convertMap(dashboardList, DashboardDO::getDashletId);
        List<Long> deleteList = new ArrayList<>();

        for (DashboardRespVO respVO : oldList) {
            DashboardDO newDo = updateMap.get(respVO.getDashletId());
            if (newDo == null) {
                deleteList.add(respVO.getId());
            }
        }

        dashboardMapper.deleteByIds(deleteList);
    }

    @Override
    public void deleteDashboard(Long id) {
        // 校验存在
        validateDashboardExists(id);
        // 删除
        dashboardMapper.deleteById(id);
    }

    private void validateDashboardExists(Long id) {
        if (dashboardMapper.selectById(id) == null) {
            throw exception(DASHBOARD_NOT_EXISTS);
        }
    }

}

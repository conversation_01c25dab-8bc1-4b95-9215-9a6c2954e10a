package cn.iocoder.yudao.module.ext.controller.admin.dashlet.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 工作台模块管理新增/修改 Request VO")
@Data
public class DashletSaveReqVO {

    @Schema(description = "模块ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23748")
    private Long id;

    @Schema(description = "模块名称", example = "芋艿")
    private String name;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    @Schema(description = "默认行")
    private Integer defRow;

    @Schema(description = "默认列")
    private Integer defCol;

    @Schema(description = "模块icon")
    private String icon;

    @Schema(description = "模块url", example = "https://www.iocoder.cn")
    private String url;

    @Schema(description = "模块描述", example = "你说的对")
    private String description;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "前端组件名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "前端组件名称不能为空")
    private String componentName;

}

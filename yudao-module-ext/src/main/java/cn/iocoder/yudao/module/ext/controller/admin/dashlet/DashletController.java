package cn.iocoder.yudao.module.ext.controller.admin.dashlet;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ext.controller.admin.dashlet.vo.*;
import cn.iocoder.yudao.module.ext.dal.dataobject.dashlet.DashletDO;
import cn.iocoder.yudao.module.ext.service.dashlet.DashletService;

@Tag(name = "管理后台 - 工作台模块管理")
@RestController
@RequestMapping("/ext/dashlet")
@Validated
public class DashletController {

    @Resource
    private DashletService dashletService;

    @PostMapping("/create")
    @Operation(summary = "创建工作台模块管理")
    @PreAuthorize("@ss.hasPermission('ext:dashlet:create')")
    public CommonResult<Long> createDashlet(@Valid @RequestBody DashletSaveReqVO createReqVO) {
        return success(dashletService.createDashlet(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新工作台模块管理")
    @PreAuthorize("@ss.hasPermission('ext:dashlet:update')")
    public CommonResult<Boolean> updateDashlet(@Valid @RequestBody DashletSaveReqVO updateReqVO) {
        dashletService.updateDashlet(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除工作台模块管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ext:dashlet:delete')")
    public CommonResult<Boolean> deleteDashlet(@RequestParam("id") Long id) {
        dashletService.deleteDashlet(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除工作台模块管理")
                @PreAuthorize("@ss.hasPermission('ext:dashlet:delete')")
    public CommonResult<Boolean> deleteDashletList(@RequestParam("ids") List<Long> ids) {
        dashletService.deleteDashletListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得工作台模块管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ext:dashlet:query')")
    public CommonResult<DashletRespVO> getDashlet(@RequestParam("id") Long id) {
        DashletDO dashlet = dashletService.getDashlet(id);
        return success(BeanUtils.toBean(dashlet, DashletRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得工作台模块管理分页")
    @PreAuthorize("@ss.hasPermission('ext:dashlet:query')")
    public CommonResult<PageResult<DashletRespVO>> getDashletPage(@Valid DashletPageReqVO pageReqVO) {
        PageResult<DashletDO> pageResult = dashletService.getDashletPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DashletRespVO.class));
    }

    @PostMapping("/my-permission")
    @Operation(summary = "获得当前用户权限范围内的工作台模块")
    @Parameter(name = "publicNames", description = "公共模块的名称列表", required = true)
    public CommonResult<?> getMyPermissionDashletList(@RequestBody List<String> publicNames) {
        List<DashletDO> dashletList = dashletService.getMyPermissionDashletList(publicNames);
        return success(BeanUtils.toBean(dashletList, DashletRespVO.class));
    }

}

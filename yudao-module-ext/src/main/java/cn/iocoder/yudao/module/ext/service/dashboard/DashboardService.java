package cn.iocoder.yudao.module.ext.service.dashboard;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ext.controller.admin.dashboard.vo.*;
import cn.iocoder.yudao.module.ext.dal.dataobject.dashboard.DashboardDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 工作台设置 Service 接口
 *
 * <AUTHOR>
 */
public interface DashboardService {

    List<DashboardRespVO> getMyList(String group);

    /**
     * 配置我的工作台
     * @param list 配置信息
     */
    void configMyDashboards(@Valid List<DashboardSaveReqVO> list);

    /**
     * 删除工作台设置
     *
     * @param id 编号
     */
    void deleteDashboard(Long id);

}

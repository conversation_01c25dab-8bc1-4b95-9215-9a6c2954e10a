package cn.iocoder.yudao.module.ext.service.dashlet;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ext.controller.admin.dashlet.vo.*;
import cn.iocoder.yudao.module.ext.dal.dataobject.dashlet.DashletDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 工作台模块管理 Service 接口
 *
 * <AUTHOR>
 */
public interface DashletService {

    /**
     * 创建工作台模块管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDashlet(@Valid DashletSaveReqVO createReqVO);

    /**
     * 更新工作台模块管理
     *
     * @param updateReqVO 更新信息
     */
    void updateDashlet(@Valid DashletSaveReqVO updateReqVO);

    /**
     * 删除工作台模块管理
     *
     * @param id 编号
     */
    void deleteDashlet(Long id);

    /**
    * 批量删除工作台模块管理
    *
    * @param ids 编号
    */
    void deleteDashletListByIds(List<Long> ids);

    /**
     * 获得工作台模块管理
     *
     * @param id 编号
     * @return 工作台模块管理
     */
    DashletDO getDashlet(Long id);

    /**
     * 获得工作台模块管理分页
     *
     * @param pageReqVO 分页查询
     * @return 工作台模块管理分页
     */
    PageResult<DashletDO> getDashletPage(DashletPageReqVO pageReqVO);

    /**
     * 获得我有权限的工作台模块管理列表
     *
     * @return 工作台模块管理列表
     */
    List<DashletDO> getMyPermissionDashletList(List<String> publicNames);


}

package cn.iocoder.yudao.module.ext.controller.admin.dashboard.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 工作台设置分页 Request VO")
@Data
public class DashboardPageReqVO extends PageParam {

    @Schema(description = "模块id", example = "26183")
    private Long dashletId;

    @Schema(description = "用户id", example = "28984")
    private Long userId;

    @Schema(description = "工作台组")
    private String dashGroup;

    @Schema(description = "是否默认")
    private Boolean isDefault;

    @Schema(description = "菜单状态", example = "2")
    private Integer status;

}
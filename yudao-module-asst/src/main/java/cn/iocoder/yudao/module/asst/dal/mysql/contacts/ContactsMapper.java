package cn.iocoder.yudao.module.asst.dal.mysql.contacts;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.asst.controller.admin.contacts.vo.ContactsPageReqVO;
import cn.iocoder.yudao.module.asst.dal.dataobject.contacts.ContactsDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 联系人 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContactsMapper extends BaseMapperX<ContactsDO> {

    default PageResult<ContactsDO> selectPage(ContactsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContactsDO>()
                .likeIfPresent(ContactsDO::getName, reqVO.getName())
                .likeIfPresent(ContactsDO::getPhone, reqVO.getPhone())
                .likeIfPresent(ContactsDO::getEmail, reqVO.getEmail())
                .likeIfPresent(ContactsDO::getWeixin, reqVO.getWeixin())
                .likeIfPresent(ContactsDO::getCompany, reqVO.getCompany())
                .likeIfPresent(ContactsDO::getSchool, reqVO.getSchool())
                .eqIfPresent(ContactsDO::getTags, reqVO.getTags())
                .eqIfPresent(ContactsDO::getOsCode, reqVO.getOsCode())
                .eqIfPresent(ContactsDO::getState, reqVO.getState())
                .eqIfPresent(ContactsDO::getUserId, reqVO.getUserId())
                .orderByDesc(ContactsDO::getId));
    }

}